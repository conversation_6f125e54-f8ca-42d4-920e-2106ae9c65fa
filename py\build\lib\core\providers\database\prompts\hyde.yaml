hyde:
  template: >
    ### Instruction:

    Given the query that follows write a double newline separated list of {num_outputs} single paragraph distinct attempted answers to the given query.


    DO NOT generate any single answer which is likely to require information from multiple distinct documents,

    EACH single answer will be used to carry out a cosine similarity semantic search over distinct indexed documents, such as varied medical documents.


    FOR EXAMPLE if asked `how do the key themes of Great Gatsby compare with 1984`, the two attempted answers would be

    `The key themes of Great Gatsby are ... ANSWER_CONTINUED` and `The key themes of 1984 are ... ANSWER_CONTINUED`, where `ANSWER_CONTINUED` IS TO BE COMPLETED BY YOU in your response.


    Here is the original user query to be transformed into answers:


    ### Query:

    {message}


    ### Response:
  input_types:
    num_outputs: int
    message: str
