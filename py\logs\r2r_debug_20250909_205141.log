2025-09-09 20:51:45,707 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-09 20:51:45,709 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-09 20:51:46,710 - httpcore.connection - DEBUG - trace:47 - connect_tcp.started host='raw.githubusercontent.com' port=443 local_address=None timeout=5 socket_options=None
2025-09-09 20:51:46,717 - httpcore.connection - DEBUG - trace:47 - connect_tcp.failed exception=ConnectError(gaierror(11004, 'getaddrinfo failed'))
2025-09-09 20:51:48,508 - LiteLLM - DEBUG - <module>:179 - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-09 20:51:49,678 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-09 20:51:49,679 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-09 20:51:50,082 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-09 20:51:50,083 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-09 20:51:50,459 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-09 20:51:50,460 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
