2025-09-09 19:51:03,127 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 19:51:03,130 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 19:51:04,395 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=10809 local_address=None timeout=5 socket_options=None
2025-09-09 19:51:04,412 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000196BE1B2780>
2025-09-09 19:51:04,413 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-09-09 19:51:04,425 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-09-09 19:51:04,425 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-09-09 19:51:04,426 - httpcore.http11 - DEBUG - send_request_body.complete
2025-09-09 19:51:04,426 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-09-09 19:51:04,427 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-09-09 19:51:04,427 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000196BE195ED0> server_hostname='raw.githubusercontent.com' timeout=5
2025-09-09 19:51:04,851 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000196BE1B34A0>
2025-09-09 19:51:04,853 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-09-09 19:51:04,854 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-09-09 19:51:04,854 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-09-09 19:51:04,855 - httpcore.http11 - DEBUG - send_request_body.complete
2025-09-09 19:51:04,856 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-09-09 19:51:05,082 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Connection', b'keep-alive'), (b'Content-Length', b'36440'), (b'Cache-Control', b'max-age=300'), (b'Content-Security-Policy', b"default-src 'none'; style-src 'unsafe-inline'; sandbox"), (b'Content-Type', b'text/plain; charset=utf-8'), (b'ETag', b'W/"d550af76ef739cf41998b2b41c0694ae0448df385dad0c2543e07bcde2a8bda4"'), (b'Strict-Transport-Security', b'max-age=31536000'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Frame-Options', b'deny'), (b'X-XSS-Protection', b'1; mode=block'), (b'X-GitHub-Request-Id', b'A2ED:39D551:15CE0A0:1CE915A:68BF7FBD'), (b'Content-Encoding', b'gzip'), (b'Accept-Ranges', b'bytes'), (b'Date', b'Tue, 09 Sep 2025 11:51:05 GMT'), (b'Via', b'1.1 varnish'), (b'X-Served-By', b'cache-bfi-krnt7300032-BFI'), (b'X-Cache', b'HIT'), (b'X-Cache-Hits', b'23'), (b'X-Timer', b'S1757418665.046708,VS0,VE0'), (b'Vary', b'Authorization,Accept-Encoding'), (b'Access-Control-Allow-Origin', b'*'), (b'Cross-Origin-Resource-Policy', b'cross-origin'), (b'X-Fastly-Request-ID', b'f3fc3d47547258b4832f07030d68cf797cc69900'), (b'Expires', b'Tue, 09 Sep 2025 11:56:05 GMT'), (b'Source-Age', b'298')])
2025-09-09 19:51:05,090 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-09-09 19:51:05,333 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-09-09 19:51:05,334 - httpcore.http11 - DEBUG - response_closed.started
2025-09-09 19:51:05,334 - httpcore.http11 - DEBUG - response_closed.complete
2025-09-09 19:51:06,351 - LiteLLM - DEBUG - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-09 19:51:07,259 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 19:51:07,264 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 19:51:07,588 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 19:51:07,589 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 19:51:07,916 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 19:51:07,918 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 19:51:13,622 - r2r.serve - ERROR - Failed to start server: core dependencies not installed: No module named 'core.providers.ocr.dotsocr.utils.prompts'
2025-09-09 19:51:13,623 - r2r.serve - ERROR - To run the server, install the required dependencies:
2025-09-09 19:51:13,623 - r2r.serve - ERROR - pip install 'r2r[core]'
2025-09-09 19:51:13,624 - asyncio - DEBUG - Using proactor: IocpProactor
