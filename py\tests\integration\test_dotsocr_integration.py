"""
Integration tests for DotsOCR Provider
"""
import pytest
import asyncio
import tempfile
import os
from PIL import Image
import io

from core.base.providers.ocr import OCRConfig
from core.providers.ocr.dotsocr import DotsOCRProvider


@pytest.mark.integration
@pytest.mark.skipif(
    not os.environ.get('DOTSOCR_INTEGRATION_TESTS'),
    reason="Integration tests require DOTSOCR_INTEGRATION_TESTS=1"
)
class TestDotsOCRIntegration:
    """Integration tests for DotsOCR Provider"""
    
    @pytest.fixture(scope="class")
    def provider(self):
        """Create a DotsOCRProvider instance for integration testing"""
        config = OCRConfig(provider="dotsocr", model="dotsocr-model")
        return DotsOCRProvider(config)
    
    @pytest.fixture
    def test_image_file(self):
        """Create a test image file"""
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            # Create a simple test image with text
            image = Image.new('RGB', (400, 200), color='white')
            # In a real test, you might add text to the image using PIL's ImageDraw
            image.save(tmp_file.name, 'PNG')
            
            yield tmp_file.name
            
            # Cleanup
            os.unlink(tmp_file.name)
    
    @pytest.fixture
    def test_pdf_file(self):
        """Create a test PDF file"""
        # Note: This is a simplified example. In a real test, you would
        # create an actual PDF with content using a library like reportlab
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            # This is just placeholder content - replace with actual PDF creation
            tmp_file.write(b'%PDF-1.4\n%fake pdf content for testing\n')
            
            yield tmp_file.name
            
            # Cleanup
            os.unlink(tmp_file.name)
    
    @pytest.mark.asyncio
    async def test_health_check_real_server(self, provider):
        """Test health check against real DotsOCR server"""
        result = await provider.health_check()
        
        # The result should contain status information
        assert 'status' in result
        assert 'server' in result
        assert 'timestamp' in result
        
        # If the server is running, status should be 'healthy'
        # If not, it should be 'unhealthy' with error information
        if result['status'] == 'healthy':
            assert 'response_time' in result
            assert result['response_time'] > 0
        else:
            assert 'error' in result
    
    @pytest.mark.asyncio
    async def test_process_image_file_real(self, provider, test_image_file):
        """Test processing a real image file"""
        try:
            result = await provider.process_pdf(file_path=test_image_file)
            
            # Check basic result structure
            assert 'success' in result
            
            if result['success']:
                assert 'results' in result
                assert 'total_pages' in result
                assert isinstance(result['results'], list)
                
                # Check individual result structure
                if result['results']:
                    first_result = result['results'][0]
                    assert 'page_no' in first_result
                    
            else:
                # If processing failed, there should be error information
                assert 'error' in result
                
        except Exception as e:
            # Log the exception for debugging
            pytest.fail(f"Image processing failed with exception: {e}")
    
    @pytest.mark.asyncio
    async def test_process_url_real(self, provider):
        """Test processing an image from a real URL"""
        # Use a publicly available test image
        test_url = "https://via.placeholder.com/300x200/000000/FFFFFF?text=Test+Image"
        
        try:
            result = await provider.process_url(test_url, is_image=True)
            
            # Check basic result structure
            assert 'success' in result
            
            if result['success']:
                assert 'results' in result
                assert 'total_pages' in result
                
            else:
                # If processing failed, there should be error information
                assert 'error' in result
                
        except Exception as e:
            # This might fail due to network issues, so we'll just log it
            pytest.skip(f"URL processing test skipped due to: {e}")
    
    @pytest.mark.asyncio
    async def test_upload_and_process_workflow(self, provider):
        """Test the complete upload and process workflow"""
        # Create a test image
        test_image = Image.new('RGB', (200, 100), color='lightblue')
        img_bytes = io.BytesIO()
        test_image.save(img_bytes, format='PNG')
        img_data = img_bytes.getvalue()
        
        try:
            # Step 1: Upload the file
            upload_result = await provider.upload_file(
                file_content=img_data,
                file_name='test_workflow.png'
            )
            
            assert 'file_id' in upload_result
            assert upload_result['file_name'] == 'test_workflow.png'
            assert upload_result['file_size'] == len(img_data)
            
            # Step 2: Process using the uploaded content
            # Note: Since process_file is not fully implemented, we'll test
            # the direct processing approach
            task = {
                'file_content': img_data,
                'file_name': 'test_workflow.png',
                'prompt_mode': 'prompt_layout_all_en'
            }
            
            process_result = await provider._execute_task(task)
            
            assert 'success' in process_result
            
        except Exception as e:
            pytest.fail(f"Upload and process workflow failed: {e}")
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self, provider):
        """Test provider performance under concurrent load"""
        # Create multiple test images
        test_images = []
        for i in range(5):  # Test with 5 concurrent requests
            image = Image.new('RGB', (100, 100), color=(i*50, i*50, i*50))
            img_bytes = io.BytesIO()
            image.save(img_bytes, format='PNG')
            test_images.append(img_bytes.getvalue())
        
        async def process_single_image(img_data, index):
            """Process a single image"""
            task = {
                'file_content': img_data,
                'file_name': f'test_load_{index}.png',
                'prompt_mode': 'prompt_layout_all_en'
            }
            return await provider._execute_task(task)
        
        try:
            # Process all images concurrently
            tasks = [
                process_single_image(img_data, i) 
                for i, img_data in enumerate(test_images)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check that most requests succeeded
            successful_results = [
                r for r in results 
                if not isinstance(r, Exception) and r.get('success', False)
            ]
            
            # At least 80% should succeed (allowing for some failures under load)
            success_rate = len(successful_results) / len(results)
            assert success_rate >= 0.8, f"Success rate too low: {success_rate}"
            
        except Exception as e:
            pytest.fail(f"Performance test failed: {e}")
    
    def test_metrics_collection(self, provider):
        """Test that metrics are being collected properly"""
        # Get initial metrics
        initial_metrics = provider.get_performance_metrics()
        
        assert 'provider_info' in initial_metrics
        assert 'total_operations' in initial_metrics
        assert initial_metrics['provider_info']['name'] == 'dotsocr'
        
        # Reset metrics and verify
        provider.reset_metrics()
        reset_metrics = provider.get_performance_metrics()
        
        assert reset_metrics['total_operations'] == 0
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, provider):
        """Test error handling and recovery mechanisms"""
        # Test with invalid input to trigger error handling
        invalid_task = {
            'file_content': b'invalid_image_data',
            'file_name': 'invalid.png',
            'prompt_mode': 'prompt_layout_all_en'
        }
        
        try:
            result = await provider._execute_task(invalid_task)
            
            # Should either succeed with error handling or fail gracefully
            assert 'success' in result
            
            if not result['success']:
                assert 'error' in result
                
        except Exception as e:
            # Exceptions should be properly typed DotsOCR exceptions
            from core.providers.ocr.dotsocr.exceptions import DotsOCRException
            assert isinstance(e, DotsOCRException), f"Unexpected exception type: {type(e)}"
    
    @pytest.mark.asyncio
    async def test_different_prompt_modes(self, provider):
        """Test different prompt modes"""
        # Create a test image
        test_image = Image.new('RGB', (200, 100), color='white')
        img_bytes = io.BytesIO()
        test_image.save(img_bytes, format='PNG')
        img_data = img_bytes.getvalue()
        
        prompt_modes = [
            'prompt_layout_all_en',
            'prompt_layout_only_en',
        ]
        
        for prompt_mode in prompt_modes:
            task = {
                'file_content': img_data,
                'file_name': f'test_{prompt_mode}.png',
                'prompt_mode': prompt_mode
            }
            
            try:
                result = await provider._execute_task(task)
                
                # Should process successfully or fail gracefully
                assert 'success' in result
                
                if result['success']:
                    assert 'results' in result
                    
            except Exception as e:
                # Log but don't fail the test for individual prompt modes
                print(f"Prompt mode {prompt_mode} failed: {e}")


if __name__ == "__main__":
    # Run integration tests if called directly
    pytest.main([__file__, "-v", "-m", "integration"])
