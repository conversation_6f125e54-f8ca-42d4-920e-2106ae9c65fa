#!/usr/bin/env python3
"""
启动脚本：加载环境变量并启动R2R服务
"""
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def load_env_file(env_path):
    """加载.env文件中的环境变量"""
    if not os.path.exists(env_path):
        print(f"警告: 环境变量文件 {env_path} 不存在")
        return
    
    with open(env_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                # 只有当环境变量不存在时才设置
                if key not in os.environ:
                    os.environ[key] = value

def main():
    # 加载环境变量文件
    env_file = Path(__file__).parent.parent / '.env'
    load_env_file(env_file)
    
    # 设置配置文件路径
    config_path = Path(__file__).parent / 'r2r' / 'r2r.toml'
    os.environ['R2R_CONFIG_PATH'] = str(config_path)
    
    print(f"加载环境变量文件: {env_file}")
    print(f"使用配置文件: {config_path}")
    print(f"服务将在 {os.getenv('R2R_HOST', '0.0.0.0')}:{os.getenv('R2R_PORT', '7272')} 启动")
    
    # 导入并启动服务
    try:
        from r2r.serve import main as serve_main
        serve_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装R2R依赖: pip install 'r2r[core]'")
        sys.exit(1)

if __name__ == "__main__":
    main()
