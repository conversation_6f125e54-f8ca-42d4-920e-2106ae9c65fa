# Example r2r.toml configuration with DotsOCR enabled
# This file demonstrates how to configure AgentRag to use DotsOCR for OCR processing

[app]
# Basic app configuration
default_max_documents_per_user = 10_000
default_max_chunks_per_user = 10_000_000
default_max_collections_per_user = 5_000
default_max_upload_size = 214748364800  # 200 GB

# LLM configuration
fast_llm = "openai/gpt-4o-mini"
quality_llm = "openai/gpt-4o"
vlm = "openai/gpt-4o"  # Visual language model for image processing
audio_lm = "openai/whisper-1"

[agent]
rag_agent_static_prompt = "static_rag_agent"
rag_agent_dynamic_prompt = "dynamic_rag_agent"
rag_tools = ["search_file_descriptions", "search_file_knowledge", "get_file_content"]

[auth]
provider = "r2r"
access_token_lifetime_in_minutes = 60000
refresh_token_lifetime_in_days = 7
require_authentication = false
require_email_verification = false
default_admin_email = "<EMAIL>"
default_admin_password = "change_me_immediately"

[completion]
provider = "r2r"
concurrent_request_limit = 64
request_timeout = 60

  [completion.generation_config]
  temperature = 0.1
  top_p = 1
  max_tokens_to_sample = 4_096
  stream = false
  add_generation_kwargs = { }

[crypto]
provider = "bcrypt"

[file]
provider = "postgres"

[database]
default_collection_name = "Default"
default_collection_description = "Your default collection."

  [database.graph_creation_settings]
    entity_types = []
    relation_types = []
    automatic_deduplication = true

[embedding]
provider = "openai"
base_model = "text-embedding-3-small"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256

[completion_embedding]
provider = "openai"
base_model = "text-embedding-3-small"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256

[ingestion]
provider = "r2r"
chunking_strategy = "recursive"
chunk_size = 1_024
chunk_overlap = 512
excluded_parsers = []
automatic_extraction = true
vlm_batch_size = 20
max_concurrent_vlm_tasks = 20
vlm_ocr_one_page_per_chunk = true

  [ingestion.chunk_enrichment_settings]
    enable_chunk_enrichment = false
    n_chunks = 2

  [ingestion.extra_parsers]
    # Enable OCR processing for PDF files
    # This tells the ingestion system to use OCR for PDF processing
    pdf = ["zerox", "ocr"]

# ==================== DotsOCR Configuration ====================
# This section configures the DotsOCR provider for OCR processing
[ocr]
provider = "dotsocr"           # Use DotsOCR provider
model = "dotsocr-model"        # Model name (can be overridden by DOTSOCR_MODEL env var)

# Note: Additional DotsOCR configuration is done via environment variables:
# 
# Required environment variables:
# DOTSOCR_IP=************              # IP address of DotsOCR vLLM server
# DOTSOCR_PORT=8008                     # Port of DotsOCR vLLM server
# 
# Optional environment variables (with defaults):
# DOTSOCR_MODEL=dotsocr-model           # Model name
# DOTSOCR_API_KEY=0                     # API key (if required)
# DOTSOCR_DPI=200                       # DPI for PDF rendering
# DOTSOCR_TEMPERATURE=0.1               # Sampling temperature
# DOTSOCR_TOP_P=1.0                     # Top-p sampling parameter
# DOTSOCR_MAX_TOKENS=16384              # Maximum completion tokens
# DOTSOCR_NUM_THREADS=8                 # Number of processing threads

[orchestration]
provider = "simple"

[email]
provider = "console_mock"

[scheduler]
provider = "apscheduler"

# ==================== Environment Variables Setup ====================
# To use this configuration, set the following environment variables:
#
# # Required DotsOCR server configuration
# export DOTSOCR_IP="************"
# export DOTSOCR_PORT="8008"
# export DOTSOCR_MODEL="dotsocr-model"
#
# # Optional performance tuning
# export DOTSOCR_DPI="200"
# export DOTSOCR_TEMPERATURE="0.1"
# export DOTSOCR_NUM_THREADS="8"
#
# # OpenAI API key (for other LLM services)
# export OPENAI_API_KEY="your-openai-api-key"
#
# # Database configuration (if using PostgreSQL)
# export R2R_DATABASE_URL="postgresql://user:password@localhost:5432/r2r"

# ==================== Usage Instructions ====================
# 1. Ensure DotsOCR vLLM server is running and accessible
# 2. Set the required environment variables
# 3. Start AgentRag with this configuration:
#    python -m r2r serve --config-path=dotsocr_config_example.toml
# 4. Upload documents with images or PDFs - they will be processed with DotsOCR
# 5. Monitor OCR performance using the health check endpoint
