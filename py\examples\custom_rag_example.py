"""
自定义RAG接口使用示例
"""
import asyncio
import json
from pathlib import Path
from r2r import R2RClient


class CustomRAGExample:
    """自定义RAG功能使用示例"""
    
    def __init__(self, base_url: str = "http://localhost:7272"):
        self.client = R2RClient(base_url)
    
    async def example_custom_document_upload(self):
        """自定义文档上传示例"""
        print("=== 自定义文档上传示例 ===")
        
        # 创建测试文档
        test_file = Path("test_document.txt")
        test_file.write_text("这是一个测试文档，包含自定义处理的内容。")
        
        try:
            # 使用自定义上传接口
            response = await self.client.make_request(
                "POST",
                "custom/documents/upload",
                files={"file": open(test_file, "rb")},
                data={
                    "metadata": json.dumps({"source": "custom_upload", "type": "test"}),
                    "custom_config": json.dumps({
                        "parser": "custom_parser",
                        "vectorization": {
                            "model": "custom_embedding",
                            "batch_size": 50
                        }
                    })
                }
            )
            print(f"上传结果: {response}")
            
        except Exception as e:
            print(f"上传失败: {e}")
        finally:
            # 清理测试文件
            if test_file.exists():
                test_file.unlink()
    
    async def example_custom_search(self):
        """自定义检索示例"""
        print("\n=== 自定义检索示例 ===")
        
        try:
            # 使用自定义检索接口
            response = await self.client.make_request(
                "POST",
                "custom/retrieval/search",
                json={
                    "query": "测试文档内容",
                    "search_mode": "hybrid",
                    "custom_filters": {
                        "source": "custom_upload",
                        "score_threshold": 0.8
                    },
                    "custom_settings": {
                        "rerank": True,
                        "expand_query": True,
                        "max_results": 5
                    }
                }
            )
            print(f"检索结果: {response}")
            
        except Exception as e:
            print(f"检索失败: {e}")
    
    async def example_custom_rag(self):
        """自定义RAG示例"""
        print("\n=== 自定义RAG示例 ===")
        
        try:
            # 同步RAG请求
            response = await self.client.make_request(
                "POST",
                "custom/retrieval/rag",
                json={
                    "query": "请解释测试文档的内容",
                    "generation_config": {
                        "model": "deepseek/deepseek-chat",
                        "temperature": 0.7,
                        "max_tokens": 1000
                    },
                    "search_settings": {
                        "limit": 5,
                        "use_hybrid_search": True
                    },
                    "custom_prompt": "基于以下上下文回答问题：\n{context}\n\n问题：{query}\n\n回答：",
                    "custom_context": {
                        "include_metadata": True,
                        "format": "detailed"
                    },
                    "stream": False
                }
            )
            print(f"RAG结果: {response}")
            
        except Exception as e:
            print(f"RAG失败: {e}")
    
    async def example_custom_streaming_rag(self):
        """自定义流式RAG示例"""
        print("\n=== 自定义流式RAG示例 ===")
        
        try:
            # 流式RAG请求
            async with self.client.stream_request(
                "POST",
                "custom/retrieval/rag",
                json={
                    "query": "详细解释测试文档的内容和意义",
                    "generation_config": {
                        "model": "deepseek/deepseek-chat",
                        "temperature": 0.5,
                        "stream": True
                    },
                    "custom_prompt": "作为专业分析师，基于以下上下文详细分析：\n{context}\n\n分析要求：{query}\n\n分析结果：",
                    "stream": True
                }
            ) as response:
                print("流式响应:")
                async for chunk in response:
                    if chunk:
                        print(chunk, end="", flush=True)
                print("\n")
                
        except Exception as e:
            print(f"流式RAG失败: {e}")
    
    async def example_custom_conversation(self):
        """自定义会话示例"""
        print("\n=== 自定义会话示例 ===")
        
        try:
            # 创建自定义会话
            conversation_response = await self.client.make_request(
                "POST",
                "custom/conversations",
                json={
                    "name": "自定义RAG会话",
                    "custom_config": {
                        "context_window": 10,
                        "memory_type": "enhanced",
                        "personalization": True
                    }
                }
            )
            
            conversation_id = conversation_response["conversation_id"]
            print(f"创建会话: {conversation_id}")
            
            # 添加自定义消息
            message_response = await self.client.make_request(
                "POST",
                f"custom/conversations/{conversation_id}/messages",
                json={
                    "content": "请分析我上传的测试文档",
                    "role": "user",
                    "custom_metadata": {
                        "intent": "analysis",
                        "priority": "high",
                        "context_type": "document_analysis"
                    }
                }
            )
            print(f"添加消息: {message_response}")
            
        except Exception as e:
            print(f"会话操作失败: {e}")
    
    async def run_all_examples(self):
        """运行所有示例"""
        print("开始运行自定义RAG接口示例...")
        
        # 登录（如果需要认证）
        try:
            await self.client.login("<EMAIL>", "change_me_immediately")
            print("登录成功")
        except:
            print("跳过登录（可能不需要认证）")
        
        # 运行各个示例
        await self.example_custom_document_upload()
        await self.example_custom_search()
        await self.example_custom_rag()
        await self.example_custom_streaming_rag()
        await self.example_custom_conversation()
        
        print("\n所有示例运行完成！")


async def main():
    """主函数"""
    example = CustomRAGExample()
    await example.run_all_examples()


if __name__ == "__main__":
    asyncio.run(main())


# 同步版本示例
def sync_example():
    """同步版本的使用示例"""
    from r2r import R2RClient
    
    client = R2RClient("http://localhost:7272")
    
    try:
        # 登录
        client.login("<EMAIL>", "change_me_immediately")
        
        # 自定义文档上传
        with open("test.txt", "w") as f:
            f.write("这是同步版本的测试文档")
        
        upload_result = client.documents.create(
            file_path="test.txt",
            metadata={"source": "sync_example"}
        )
        print(f"同步上传结果: {upload_result}")
        
        # 自定义检索
        search_result = client.retrieval.search(
            query="测试文档",
            search_settings={
                "limit": 5,
                "filters": {"source": "sync_example"}
            }
        )
        print(f"同步检索结果: {search_result}")
        
        # 自定义RAG
        rag_result = client.retrieval.rag(
            query="解释测试文档内容",
            rag_generation_config={
                "model": "deepseek/deepseek-chat",
                "temperature": 0.7
            }
        )
        print(f"同步RAG结果: {rag_result}")
        
    except Exception as e:
        print(f"同步示例失败: {e}")


# 如果需要运行同步版本
# sync_example()
