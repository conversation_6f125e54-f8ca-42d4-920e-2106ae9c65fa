2025-09-10 12:55:12,110 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 12:55:12,112 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 12:55:13,167 - httpcore.connection - DEBUG - trace:47 - connect_tcp.started host='127.0.0.1' port=10809 local_address=None timeout=5 socket_options=None
2025-09-10 12:55:13,167 - httpcore.connection - DEBUG - trace:47 - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001C5EED316D0>
2025-09-10 12:55:13,168 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.started request=<Request [b'CONNECT']>
2025-09-10 12:55:13,170 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.complete
2025-09-10 12:55:13,171 - httpcore.http11 - DEBUG - trace:47 - send_request_body.started request=<Request [b'CONNECT']>
2025-09-10 12:55:13,171 - httpcore.http11 - DEBUG - trace:47 - send_request_body.complete
2025-09-10 12:55:13,171 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.started request=<Request [b'CONNECT']>
2025-09-10 12:55:13,171 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-09-10 12:55:13,171 - httpcore.proxy - DEBUG - trace:47 - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001C5EED24F50> server_hostname='raw.githubusercontent.com' timeout=5
2025-09-10 12:55:14,162 - httpcore.proxy - DEBUG - trace:47 - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001C5EED09F40>
2025-09-10 12:55:14,162 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.started request=<Request [b'GET']>
2025-09-10 12:55:14,162 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.complete
2025-09-10 12:55:14,162 - httpcore.http11 - DEBUG - trace:47 - send_request_body.started request=<Request [b'GET']>
2025-09-10 12:55:14,163 - httpcore.http11 - DEBUG - trace:47 - send_request_body.complete
2025-09-10 12:55:14,163 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.started request=<Request [b'GET']>
2025-09-10 12:55:14,851 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Connection', b'keep-alive'), (b'Content-Length', b'36349'), (b'Cache-Control', b'max-age=300'), (b'Content-Security-Policy', b"default-src 'none'; style-src 'unsafe-inline'; sandbox"), (b'Content-Type', b'text/plain; charset=utf-8'), (b'ETag', b'W/"a54afa8c50cb480adc124fe974f7da4724ad4c9e045d413bd2918005aea7d354"'), (b'Strict-Transport-Security', b'max-age=31536000'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Frame-Options', b'deny'), (b'X-XSS-Protection', b'1; mode=block'), (b'X-GitHub-Request-Id', b'7719:1B1E86:1107CF:166D7F:68C0E7B6'), (b'Content-Encoding', b'gzip'), (b'Accept-Ranges', b'bytes'), (b'Date', b'Wed, 10 Sep 2025 04:55:14 GMT'), (b'Via', b'1.1 varnish'), (b'X-Served-By', b'cache-bfi-kbfi7400117-BFI'), (b'X-Cache', b'HIT'), (b'X-Cache-Hits', b'16'), (b'X-Timer', b'S1757480115.983279,VS0,VE0'), (b'Vary', b'Authorization,Accept-Encoding'), (b'Access-Control-Allow-Origin', b'*'), (b'Cross-Origin-Resource-Policy', b'cross-origin'), (b'X-Fastly-Request-ID', b'3c343d20c459fd7a7b178d39352ae8c35663b70b'), (b'Expires', b'Wed, 10 Sep 2025 05:00:14 GMT'), (b'Source-Age', b'212')])
2025-09-10 12:55:14,852 - httpcore.http11 - DEBUG - trace:47 - receive_response_body.started request=<Request [b'GET']>
2025-09-10 12:55:15,135 - httpcore.http11 - DEBUG - trace:47 - receive_response_body.complete
2025-09-10 12:55:15,135 - httpcore.http11 - DEBUG - trace:47 - response_closed.started
2025-09-10 12:55:15,135 - httpcore.http11 - DEBUG - trace:47 - response_closed.complete
2025-09-10 12:55:15,939 - LiteLLM - DEBUG - <module>:179 - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-10 12:55:16,654 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 12:55:16,655 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 12:55:16,895 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 12:55:16,897 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 12:55:17,124 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 12:55:17,128 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
