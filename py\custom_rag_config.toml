# 自定义RAG配置文件示例
[app]
# 启用自定义RAG功能
enable_custom_rag = true

# 项目名称
project_name = "custom_rag_project"

# 用户限制
default_max_documents_per_user = 10_000
default_max_chunks_per_user = 10_000_000
default_max_collections_per_user = 5_000
default_max_upload_size = 214748364800

# LLM配置
fast_llm = "deepseek/deepseek-chat"
quality_llm = "deepseek/deepseek-chat"
vlm = "openai/qwen-vl-max-latest"
audio_lm = "openai/whisper-1"

[custom_rag]
# 自定义RAG配置
enabled = true

# 自定义文档处理配置
[custom_rag.document_processing]
# 自定义解析器
custom_parser = "custom_parser"
# 支持的文件类型
supported_formats = ["pdf", "docx", "txt", "md", "html"]
# 自定义OCR配置
ocr_enabled = true
ocr_provider = "custom_ocr"

# 自定义向量化配置
[custom_rag.vectorization]
# 自定义embedding模型
embedding_model = "custom_embedding_model"
# 向量维度
vector_dimension = 1536
# 批处理大小
batch_size = 100

# 自定义检索配置
[custom_rag.retrieval]
# 默认检索模式
default_search_mode = "hybrid"
# 检索结果数量
default_limit = 10
# 相似度阈值
similarity_threshold = 0.7
# 自定义重排序
rerank_enabled = true
rerank_model = "custom_rerank_model"

# 自定义生成配置
[custom_rag.generation]
# 默认生成模型
default_model = "deepseek/deepseek-chat"
# 默认温度
default_temperature = 0.7
# 最大token数
max_tokens = 2048
# 自定义提示词模板
custom_prompt_template = """
基于以下上下文信息回答问题：

上下文：
{context}

问题：{query}

请提供准确、详细的回答：
"""

# 自定义存储配置
[custom_rag.storage]
# 自定义数据库配置
custom_db_enabled = false
custom_db_url = ""
# 自定义向量数据库
custom_vector_db = "postgresql"
# 索引配置
index_type = "hnsw"
index_params = { m = 16, ef_construction = 200 }

# 数据库配置
[database]
provider = "postgres"
user = "postgres"
password = "postgres"
host = "localhost"
port = 5432
db_name = "postgres"
project_name = "custom_rag_project"

# 向量数据库配置
[vector_database]
provider = "postgres"
collection_name = "custom_vectors"

# Embedding配置
[embedding]
provider = "openai"
base_model = "text-embedding-3-small"
base_dimension = 1536
batch_size = 128
add_title_as_prefix = true

# LLM配置
[completion]
provider = "litellm"
concurrent_request_limit = 16

[completion.generation_config]
model = "deepseek/deepseek-chat"
temperature = 0.1
top_p = 1
max_tokens_to_sample = 1024
stream = false
add_generation_kwargs = {}

# 认证配置
[auth]
provider = "r2r"
access_token_lifetime_in_minutes = 60
refresh_token_lifetime_in_days = 7
require_authentication = false
require_email_verification = false
default_admin_email = "<EMAIL>"
default_admin_password = "change_me_immediately"

# 日志配置
[logging]
provider = "local"
log_table = "logs"
log_info_table = "log_info"
