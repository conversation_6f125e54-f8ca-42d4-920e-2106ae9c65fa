# DotsOCR Provider for AgentRag

This provider integrates DotsOCR functionality into the AgentRag framework, providing advanced OCR capabilities for images and PDF documents.

## Features

- **Layout Analysis**: Detect and analyze document layout including text blocks, tables, figures
- **Text Extraction**: Extract text content while preserving structure
- **Multi-format Support**: Process images (PNG, JPG, JPEG) and PDF documents
- **Async Processing**: Full async/await support for high-performance processing
- **Commercial Grade**: Production-ready with error handling, retry mechanisms, and logging

## Configuration

### Environment Variables

The DotsOCR provider uses the following environment variables for configuration:

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DOTSOCR_IP` | IP address of the DotsOCR vLLM server | `localhost` | No |
| `DOTSOCR_PORT` | Port of the DotsOCR vLLM server | `8000` | No |
| `DOTSOCR_MODEL` | Model name to use for inference | `dotsocr-model` | No |
| `DOTSOCR_API_KEY` | API key for authentication (if required) | `0` | No |
| `DOTSOCR_DPI` | DPI for PDF rendering | `200` | No |
| `DOTSOCR_TEMPERATURE` | Sampling temperature for inference | `0.1` | No |
| `DOTSOCR_TOP_P` | Top-p sampling parameter | `1.0` | No |
| `DOTSOCR_MAX_TOKENS` | Maximum completion tokens | `16384` | No |
| `DOTSOCR_NUM_THREADS` | Number of processing threads | `8` | No |

### r2r.toml Configuration

To enable DotsOCR in your AgentRag application, add the following to your `r2r.toml` configuration file:

```toml
[ocr]
provider = "dotsocr"
model = "dotsocr-model"
```

## Setup Instructions

### 1. Environment Setup

Create a `.env` file or set environment variables:

```bash
# DotsOCR Server Configuration
export DOTSOCR_IP="************"
export DOTSOCR_PORT="8008"
export DOTSOCR_MODEL="dotsocr-model"

# Optional: Performance tuning
export DOTSOCR_DPI="200"
export DOTSOCR_TEMPERATURE="0.1"
export DOTSOCR_NUM_THREADS="8"
```

### 2. Server Requirements

Ensure you have a DotsOCR vLLM server running and accessible at the configured IP and port. The server should:

- Support the OpenAI-compatible API format
- Have the DotsOCR model loaded
- Be accessible from your AgentRag backend

### 3. Dependencies

The DotsOCR provider requires the following Python packages:

- `Pillow` (PIL) for image processing
- `PyMuPDF` (fitz) for PDF processing - **Added to pyproject.toml**
- `openai` for API communication
- `requests` for HTTP requests

These dependencies are automatically installed when you install AgentRag with the `core` optional dependencies:

```bash
pip install -e ".[core]"
```

The PyMuPDF library has been added to the `pyproject.toml` file under the `core` optional dependencies.

## Usage Examples

### Basic OCR Processing

```python
from core.providers.ocr.dotsocr import DotsOCRProvider
from core.base.providers.ocr import OCRConfig

# Create provider
config = OCRConfig(provider="dotsocr", model="dotsocr-model")
provider = DotsOCRProvider(config)

# Process a PDF file
result = await provider.process_pdf(file_path="document.pdf")

# Process an image URL
result = await provider.process_url(
    url="https://example.com/image.png",
    is_image=True
)
```

### Advanced Configuration

```python
# Custom processing parameters
result = await provider.process_pdf(
    file_path="document.pdf",
    prompt_mode="prompt_layout_all_en",
    include_image_base64=True
)
```

## Supported Prompt Modes

- `prompt_layout_all_en`: Complete layout analysis with text extraction (default)
- `prompt_layout_only_en`: Layout detection only, no text extraction
- `prompt_grounding_ocr`: Grounding-based OCR for specific regions

## Error Handling

The provider includes comprehensive error handling:

- **Connection Errors**: Automatic retry with exponential backoff
- **Processing Errors**: Graceful degradation and detailed error messages
- **Resource Management**: Automatic cleanup of temporary files
- **Logging**: Detailed logging for debugging and monitoring

## Performance Considerations

- **Concurrent Processing**: Configurable thread limits for optimal performance
- **Memory Management**: Efficient handling of large documents
- **Caching**: Temporary file management for processing efficiency
- **Scalability**: Designed for high-throughput production environments

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check if the DotsOCR server is running and accessible
2. **Model Not Found**: Verify the model name matches the server configuration
3. **Memory Issues**: Adjust `DOTSOCR_NUM_THREADS` for your system resources
4. **Timeout Errors**: Check network connectivity and server performance

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger("core.providers.ocr.dotsocr").setLevel(logging.DEBUG)
```

## License

This provider is part of the AgentRag project and follows the same licensing terms.
