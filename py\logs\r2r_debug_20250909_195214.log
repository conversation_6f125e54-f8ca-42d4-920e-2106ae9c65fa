2025-09-09 19:52:17,514 - <PERSON><PERSON>LLM - DEBUG - Using AiohttpTransport...
2025-09-09 19:52:17,516 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 19:52:18,383 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=10809 local_address=None timeout=5 socket_options=None
2025-09-09 19:52:18,385 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002763B5CA600>
2025-09-09 19:52:18,386 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-09-09 19:52:18,396 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-09-09 19:52:18,397 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-09-09 19:52:18,397 - httpcore.http11 - DEBUG - send_request_body.complete
2025-09-09 19:52:18,397 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-09-09 19:52:18,398 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-09-09 19:52:18,399 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002763B605ED0> server_hostname='raw.githubusercontent.com' timeout=5
2025-09-09 19:52:18,836 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002763B623740>
2025-09-09 19:52:18,836 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-09-09 19:52:18,836 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-09-09 19:52:18,837 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-09-09 19:52:18,837 - httpcore.http11 - DEBUG - send_request_body.complete
2025-09-09 19:52:18,837 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-09-09 19:52:19,058 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Connection', b'keep-alive'), (b'Content-Length', b'36440'), (b'Cache-Control', b'max-age=300'), (b'Content-Security-Policy', b"default-src 'none'; style-src 'unsafe-inline'; sandbox"), (b'Content-Type', b'text/plain; charset=utf-8'), (b'ETag', b'W/"d550af76ef739cf41998b2b41c0694ae0448df385dad0c2543e07bcde2a8bda4"'), (b'Strict-Transport-Security', b'max-age=31536000'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Frame-Options', b'deny'), (b'X-XSS-Protection', b'1; mode=block'), (b'X-GitHub-Request-Id', b'A2ED:39D551:15CE0A0:1CE915A:68BF7FBD'), (b'Content-Encoding', b'gzip'), (b'Accept-Ranges', b'bytes'), (b'Date', b'Tue, 09 Sep 2025 11:52:19 GMT'), (b'Via', b'1.1 varnish'), (b'X-Served-By', b'cache-bfi-kbfi7400051-BFI'), (b'X-Cache', b'HIT'), (b'X-Cache-Hits', b'6'), (b'X-Timer', b'S1757418739.031737,VS0,VE0'), (b'Vary', b'Authorization,Accept-Encoding'), (b'Access-Control-Allow-Origin', b'*'), (b'Cross-Origin-Resource-Policy', b'cross-origin'), (b'X-Fastly-Request-ID', b'3ab2612e3fe5dfc707c0c0064dd2e7c682bbbcc9'), (b'Expires', b'Tue, 09 Sep 2025 11:57:19 GMT'), (b'Source-Age', b'72')])
2025-09-09 19:52:19,061 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-09-09 19:52:19,304 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-09-09 19:52:19,305 - httpcore.http11 - DEBUG - response_closed.started
2025-09-09 19:52:19,307 - httpcore.http11 - DEBUG - response_closed.complete
2025-09-09 19:52:20,142 - LiteLLM - DEBUG - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-09 19:52:20,798 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 19:52:20,798 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 19:52:21,018 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 19:52:21,019 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 19:52:21,235 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 19:52:21,236 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 19:52:25,214 - r2r.serve - ERROR - Failed to start server: core dependencies not installed: No module named 'core.providers.ocr.dotsocr.utils.prompts'
2025-09-09 19:52:25,215 - r2r.serve - ERROR - To run the server, install the required dependencies:
2025-09-09 19:52:25,215 - r2r.serve - ERROR - pip install 'r2r[core]'
2025-09-09 19:52:25,216 - asyncio - DEBUG - Using proactor: IocpProactor
