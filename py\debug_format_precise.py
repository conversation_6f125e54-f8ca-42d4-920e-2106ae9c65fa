#!/usr/bin/env python3
"""
精确定位格式化错误的调试工具
"""

import sys
import os
import traceback
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class FormatErrorTracker:
    """格式化错误追踪器"""
    
    def __init__(self):
        self.original_format = str.__format__
        self.original_none_format = type(None).__format__
        self.error_count = 0
        
    def install(self):
        """安装格式化错误追踪"""
        def safe_str_format(self, format_spec):
            if self is None:
                print(f"🚨 发现str.__format__调用None值！")
                print(f"格式规范: {format_spec}")
                traceback.print_stack(limit=10)
                return "None"
            return self.original_format(format_spec)
        
        def safe_none_format(self, format_spec):
            print(f"🚨 发现NoneType.__format__调用！")
            print(f"格式规范: {format_spec}")
            traceback.print_stack(limit=10)
            self.error_count += 1
            return "None"
        
        str.__format__ = safe_str_format
        type(None).__format__ = safe_none_format
        
    def uninstall(self):
        """卸载格式化错误追踪"""
        str.__format__ = self.original_format
        type(None).__format__ = self.original_none_format

def test_format_results_method():
    """测试_format_results方法"""
    print("🔍 测试_format_results方法...")
    
    try:
        # 模拟DotsOCR返回的结果
        mock_results = [
            {
                "page_no": 0,
                "input_height": 1000,
                "input_width": 800,
                "filtered": False,
                "layout_info_path": None,  # 可能为None
                "layout_image_path": None,  # 可能为None
                "md_content_path": "/tmp/test.md"
            },
            {
                "page_no": 1,
                "input_height": None,  # 可能为None
                "input_width": None,   # 可能为None
                "filtered": False,
                "md_content_path": None  # 可能为None
            }
        ]
        
        # 创建临时markdown文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write("# Test Content\nThis is test content.")
            temp_md_path = f.name
        
        # 更新第一个结果的路径
        mock_results[0]["md_content_path"] = temp_md_path
        
        # 模拟_format_results方法
        class MockProvider:
            def _read_markdown_content_safely(self, md_path):
                try:
                    if not md_path or not os.path.exists(md_path):
                        return ""
                    with open(md_path, 'r', encoding='utf-8') as f:
                        return f.read()
                except Exception:
                    return ""
            
            def _format_results(self, results, include_image_base64=False):
                if not results:
                    return {"results": []}
                
                formatted = []
                
                for i, result in enumerate(results):
                    try:
                        # 这里可能出现格式化错误
                        formatted_result = {
                            "page_no": result.get("page_no", i),
                            "input_height": result.get("input_height"),
                            "input_width": result.get("input_width"),
                            "filtered": result.get("filtered", False)
                        }
                        
                        # 处理文件路径 - 这里可能出现问题
                        if "layout_info_path" in result and result["layout_info_path"]:
                            formatted_result["layout_info_path"] = result["layout_info_path"]
                        if "layout_image_path" in result and result["layout_image_path"]:
                            formatted_result["layout_image_path"] = result["layout_image_path"]
                        if "md_content_path" in result and result["md_content_path"]:
                            formatted_result["md_content_path"] = result["md_content_path"]
                            
                            # 读取markdown内容
                            md_path = result["md_content_path"]
                            content = self._read_markdown_content_safely(md_path)
                            formatted_result["md_content"] = content
                            formatted_result["text"] = content
                            formatted_result["content"] = content
                        else:
                            formatted_result["md_content"] = ""
                            formatted_result["text"] = ""
                            formatted_result["content"] = ""
                        
                        # 处理base64图像 - 这里也可能出现问题
                        if include_image_base64 and "layout_image_path" in result and result["layout_image_path"]:
                            image_path = result["layout_image_path"]
                            if image_path and os.path.exists(image_path):
                                # 这里可能有格式化错误
                                formatted_result["layout_image_base64"] = f"data:image/jpeg;base64,test_data"
                        
                        formatted.append(formatted_result)
                        
                    except Exception as e:
                        print(f"❌ 处理结果 {i} 时出错: {e}")
                        traceback.print_exc()
                        return False
                
                return {"results": formatted}
        
        # 测试
        provider = MockProvider()
        result = provider._format_results(mock_results, include_image_base64=True)
        
        print(f"✅ _format_results测试成功，处理了 {len(result['results'])} 个结果")
        
        # 清理
        os.unlink(temp_md_path)
        return True
        
    except Exception as e:
        print(f"❌ _format_results测试失败: {e}")
        traceback.print_exc()
        return False

def test_specific_format_scenarios():
    """测试特定的格式化场景"""
    print("\n🔍 测试特定格式化场景...")
    
    scenarios = [
        # 场景1: None值在f-string中
        lambda: f"Path: {None}",
        # 场景2: None值在字符串连接中
        lambda: "Path: " + str(None),
        # 场景3: None值在format方法中
        lambda: "Path: {}".format(None),
        # 场景4: 复杂的f-string
        lambda: f"data:image/jpeg;base64,{None}",
    ]
    
    for i, scenario in enumerate(scenarios):
        try:
            result = scenario()
            print(f"  场景 {i+1}: ✅ 成功 - {result}")
        except Exception as e:
            print(f"  场景 {i+1}: ❌ 失败 - {e}")
            traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始精确定位格式化错误...\n")
    
    # 安装格式化错误追踪
    tracker = FormatErrorTracker()
    tracker.install()
    
    try:
        # 测试1: 特定格式化场景
        test_specific_format_scenarios()
        
        # 测试2: _format_results方法
        test_format_results_method()
        
        print(f"\n📊 检测到 {tracker.error_count} 个格式化错误")
        
    finally:
        # 卸载追踪器
        tracker.uninstall()
    
    print("\n✅ 精确定位完成")

if __name__ == "__main__":
    main()
