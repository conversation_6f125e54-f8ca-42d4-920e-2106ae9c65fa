2025-09-09 21:08:44,095 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-09 21:08:44,101 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-09 21:08:45,244 - httpcore.connection - DEBUG - trace:47 - connect_tcp.started host='raw.githubusercontent.com' port=443 local_address=None timeout=5 socket_options=None
2025-09-09 21:08:45,255 - httpcore.connection - DEBUG - trace:47 - connect_tcp.failed exception=ConnectError(gaierror(11004, 'getaddrinfo failed'))
2025-09-09 21:08:46,859 - LiteLLM - DEBUG - <module>:179 - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-09 21:08:48,078 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-09 21:08:48,080 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-09 21:08:48,476 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-09 21:08:48,477 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-09 21:08:48,849 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-09 21:08:48,849 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
