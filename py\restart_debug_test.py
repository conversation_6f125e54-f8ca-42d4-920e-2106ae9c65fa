#!/usr/bin/env python3
"""
重启调试测试脚本
"""
import os
import sys
import time
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🚀 重启R2R调试测试")
    print("=" * 50)
    
    # 设置调试环境变量
    env = os.environ.copy()
    env["R2R_LOG_LEVEL"] = "DEBUG"
    env["DOTSOCR_DEBUG"] = "true"
    
    print("📋 调试配置:")
    print(f"   🔧 R2R_LOG_LEVEL: DEBUG")
    print(f"   🔧 DOTSOCR_DEBUG: true")
    print()
    
    print("🔍 新增的调试标识:")
    print("   🔍 OCR parser yielded chunk - OCR原始输出")
    print("   🔍 OCR CONTENTS BEFORE FILTERING - 过滤前的内容")
    print("   🔍 OCR CONTENTS AFTER FILTERING - 过滤后的内容")
    print("   🚨 CONTENT FILTERING ISSUE - 内容过滤问题")
    print("   🔍 PROCESSING PAGE - 处理每个页面")
    print("   📦 CREATED DOCUMENTCHUNK - 创建的文档分片")
    print()
    
    print("⚠️  注意事项:")
    print("   - 现在调试代码在正确的执行路径中")
    print("   - 会显示OCR数据收集和处理的完整流程")
    print("   - 重点关注content的数据类型变化")
    print("   - 查看是否有内容被过滤掉")
    print("=" * 50)
    print()
    
    # 当前目录
    current_dir = Path(__file__).parent
    
    try:
        print("📦 启动R2R调试服务...")
        print("🚀 启动中...")
        print("=" * 50)
        print()
        
        # 使用start_debug_mode.py启动
        result = subprocess.run([
            sys.executable, "start_debug_mode.py"
        ], env=env, cwd=current_dir)
        
        if result.returncode != 0:
            print(f"❌ 启动失败，返回码: {result.returncode}")
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 启动异常: {e}")

if __name__ == "__main__":
    main()
