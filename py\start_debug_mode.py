#!/usr/bin/env python3
"""
R2R调试模式启动脚本
专门用于调试DotsOCR文档分片问题
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime

def setup_debug_environment():
    """设置调试环境"""
    # 设置调试环境变量
    os.environ["R2R_LOG_LEVEL"] = "DEBUG"
    os.environ["DOTSOCR_DEBUG"] = "true"
    os.environ["PYTHONPATH"] = str(Path(__file__).parent)
    
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 创建带时间戳的日志文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"r2r_debug_{timestamp}.log"
    
    return log_file

def setup_detailed_logging(log_file):
    """设置详细的日志记录"""
    # 创建自定义格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器 - 显示重要信息
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器 - 记录所有DEBUG信息
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # 设置特定模块的日志级别为DEBUG
    debug_modules = [
        'core.parsers.media.pdf_parser',
        'core.providers.ingestion.r2r.base', 
        'core.providers.ocr.dotsocr',
        'core.providers.ocr.dotsocr.provider',
        'core.providers.ocr.dotsocr.parser',
        'core.providers.ocr.dotsocr.inference',
        'langchain.text_splitter',
        'uvicorn.error',
        'r2r'
    ]
    
    for module_name in debug_modules:
        logger = logging.getLogger(module_name)
        logger.setLevel(logging.DEBUG)
    
    return log_file

def load_env_file(env_path):
    """加载.env文件中的环境变量"""
    if not env_path.exists():
        print(f"⚠️ 环境变量文件 {env_path} 不存在")
        return
    
    with open(env_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                if key not in os.environ:
                    os.environ[key] = value

def print_debug_info(log_file):
    """打印调试信息"""
    print("🚀 R2R服务启动（调试模式）")
    print("=" * 70)
    print("📋 调试配置:")
    print(f"   🔧 日志级别: DEBUG")
    print(f"   📁 日志文件: {log_file}")
    print(f"   📺 控制台输出: INFO级别及以上")
    print(f"   📄 文件输出: DEBUG级别全部")
    print()
    print("🎯 专门调试:")
    print("   - DotsOCR文档分片问题")
    print("   - 文本分割器数据类型问题")
    print("   - OCR内容提取流程")
    print("   - 分片内容验证")
    print()
    print("🔍 关键调试标识:")
    print("   🔍 CRITICAL DEBUG - 数据处理关键点")
    print("   🚨 EMERGENCY FIX - 数据类型修复")
    print("   📦 Created DocumentChunk - 分片创建")
    print("   ❌ CRITICAL ERROR - 严重错误")
    print()
    print("⚠️  注意事项:")
    print("   - 上传PDF后查看日志中的🔍调试信息")
    print("   - 关注分片内容是否为实际文档内容")
    print("   - 如有问题，检查日志文件中的详细信息")
    print("   - 查找🚨标识的紧急修复信息")
    print("=" * 70)
    print()

def test_dotsocr_import():
    """测试DotsOCR导入"""
    print("🔍 测试DotsOCR导入...")
    try:
        from core.providers.ocr.dotsocr.utils.prompts import dict_promptmode_to_prompt
        from core.providers.ocr.dotsocr.provider import DotsOCRProvider
        print("✅ DotsOCR导入成功")
        return True
    except Exception as e:
        print(f"❌ DotsOCR导入失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 设置调试环境
        log_file = setup_debug_environment()
        
        # 设置详细日志
        setup_detailed_logging(log_file)
        
        # 打印调试信息
        print_debug_info(log_file)
        
        # 测试DotsOCR导入
        if not test_dotsocr_import():
            print("❌ DotsOCR导入失败，无法启动")
            return False
        
        # 添加当前目录到Python路径
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        # 加载环境变量文件
        env_file = current_dir.parent / '.env'
        load_env_file(env_file)
        
        # 设置配置文件路径
        config_path = current_dir / 'r2r' / 'r2r.toml'
        os.environ['R2R_CONFIG_PATH'] = str(config_path)
        
        print("📁 文件路径:")
        print(f"   📄 环境变量文件: {env_file}")
        print(f"   ⚙️  配置文件: {config_path}")
        print(f"   🌐 服务地址: {os.getenv('R2R_HOST', '0.0.0.0')}:{os.getenv('R2R_PORT', '7272')}")
        print()
        
        # 启动R2R服务
        print("📦 正在启动R2R服务...")
        from r2r.serve import main as serve_main
        serve_main()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在关闭服务...")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 记录错误到日志文件
        logger = logging.getLogger(__name__)
        logger.error(f"启动失败: {e}", exc_info=True)
        
        print(f"\n📄 详细错误信息已记录到: {log_file}")
        sys.exit(1)

if __name__ == "__main__":
    main()
