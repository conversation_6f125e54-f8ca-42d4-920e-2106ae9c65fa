"""
Pytest configuration and fixtures for DotsOCR tests
"""
import pytest
import os
import tempfile
from PIL import Image
import io


def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


@pytest.fixture(scope="session")
def test_data_dir():
    """Create a temporary directory for test data"""
    with tempfile.TemporaryDirectory(prefix="dotsocr_test_") as temp_dir:
        yield temp_dir


@pytest.fixture
def sample_image():
    """Create a sample test image"""
    image = Image.new('RGB', (300, 200), color='white')
    # Add some basic content to make it more realistic
    from PIL import ImageDraw, ImageFont
    
    draw = ImageDraw.Draw(image)
    
    # Try to use a default font, fall back to basic if not available
    try:
        font = ImageFont.load_default()
    except:
        font = None
    
    # Add some text
    draw.text((10, 10), "Test Document", fill='black', font=font)
    draw.text((10, 50), "This is a sample text for OCR testing.", fill='black', font=font)
    draw.text((10, 90), "Line 3 with some numbers: 12345", fill='black', font=font)
    
    # Add a simple rectangle (simulating a table or figure)
    draw.rectangle([10, 130, 200, 180], outline='black', width=2)
    draw.text((15, 145), "Table Cell 1", fill='black', font=font)
    draw.text((110, 145), "Table Cell 2", fill='black', font=font)
    
    return image


@pytest.fixture
def sample_image_bytes(sample_image):
    """Convert sample image to bytes"""
    img_bytes = io.BytesIO()
    sample_image.save(img_bytes, format='PNG')
    return img_bytes.getvalue()


@pytest.fixture
def sample_image_file(sample_image, test_data_dir):
    """Save sample image to a temporary file"""
    file_path = os.path.join(test_data_dir, "sample_test_image.png")
    sample_image.save(file_path, 'PNG')
    return file_path


@pytest.fixture
def sample_pdf_file(test_data_dir):
    """Create a sample PDF file for testing"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        file_path = os.path.join(test_data_dir, "sample_test_document.pdf")
        
        # Create a simple PDF with text content
        c = canvas.Canvas(file_path, pagesize=letter)
        width, height = letter
        
        # Add some text content
        c.drawString(100, height - 100, "Test PDF Document")
        c.drawString(100, height - 130, "This is a sample PDF for OCR testing.")
        c.drawString(100, height - 160, "Page 1 content with various text elements.")
        
        # Add a simple table-like structure
        c.drawString(100, height - 200, "Column 1")
        c.drawString(200, height - 200, "Column 2")
        c.drawString(300, height - 200, "Column 3")
        
        c.drawString(100, height - 220, "Row 1 Data")
        c.drawString(200, height - 220, "More Data")
        c.drawString(300, height - 220, "123.45")
        
        c.save()
        
        return file_path
        
    except ImportError:
        # If reportlab is not available, create a minimal PDF
        file_path = os.path.join(test_data_dir, "minimal_test.pdf")
        
        # Create a minimal PDF structure
        pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF Content) Tj
ET
endstream
endobj

xref
0 5
********** 65535 f 
********** 00000 n 
********** 00000 n 
********** 00000 n 
********** 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
        
        with open(file_path, 'wb') as f:
            f.write(pdf_content)
        
        return file_path


@pytest.fixture
def mock_dotsocr_server():
    """Mock DotsOCR server responses for testing"""
    class MockDotsOCRServer:
        def __init__(self):
            self.responses = {
                'health_check': {'status': 'healthy', 'model': 'test-model'},
                'ocr_response': {
                    'success': True,
                    'results': [
                        {
                            'page_no': 0,
                            'text': 'Sample extracted text',
                            'layout': [
                                {
                                    'type': 'text',
                                    'bbox': [10, 10, 200, 30],
                                    'text': 'Sample extracted text'
                                }
                            ]
                        }
                    ]
                }
            }
        
        def set_response(self, endpoint, response):
            """Set custom response for testing"""
            self.responses[endpoint] = response
        
        def get_response(self, endpoint):
            """Get response for endpoint"""
            return self.responses.get(endpoint, {'error': 'Unknown endpoint'})
    
    return MockDotsOCRServer()


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment variables"""
    original_env = os.environ.copy()
    
    # Set test environment variables
    test_env = {
        'DOTSOCR_IP': 'localhost',
        'DOTSOCR_PORT': '8000',
        'DOTSOCR_MODEL': 'test-model',
        'DOTSOCR_DPI': '200',
        'DOTSOCR_TEMPERATURE': '0.1',
        'DOTSOCR_TOP_P': '1.0',
        'DOTSOCR_MAX_TOKENS': '16384',
        'DOTSOCR_NUM_THREADS': '4'
    }
    
    os.environ.update(test_env)
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def performance_test_images():
    """Create multiple test images for performance testing"""
    images = []
    
    for i in range(10):
        # Create images with different content
        image = Image.new('RGB', (200 + i*10, 150 + i*5), color=(i*25, i*25, i*25))
        
        from PIL import ImageDraw
        draw = ImageDraw.Draw(image)
        
        # Add some text content
        draw.text((10, 10), f"Test Image {i+1}", fill='white')
        draw.text((10, 30), f"Content for performance test", fill='white')
        draw.text((10, 50), f"Image number: {i+1:03d}", fill='white')
        
        # Convert to bytes
        img_bytes = io.BytesIO()
        image.save(img_bytes, format='PNG')
        images.append(img_bytes.getvalue())
    
    return images
