"""
DotsOCR Provider for AgentRag
"""
import json
import logging
import os
import tempfile
import uuid
from typing import Any, Dict, List, Optional

from core.base.providers.ocr import OCRConfig, OCRProvider
from .parser import DotsOCRParser

logger = logging.getLogger(__name__)


class DotsOCRProvider(OCRProvider):
    """
    DotsOCR Provider implementation for AgentRag
    
    This provider integrates the DotsOCR functionality into the AgentRag framework,
    providing OCR capabilities for images and PDF documents.
    """
    
    def __init__(self, config: OCRConfig) -> None:
        if not isinstance(config, OCRConfig):
            raise ValueError(
                f"DotsOCRProvider must be initialized with a OCRConfig. Got: {config} with type {type(config)}"
            )
        super().__init__(config)
        self.config: OCRConfig = config
        
        # Get DotsOCR server configuration from environment
        self.ip = os.environ.get("DOTSOCR_IP", "localhost")
        self.port = int(os.environ.get("DOTSOCR_PORT", "8000"))
        self.model_name = config.model or os.environ.get("DOTSOCR_MODEL", "model")
        
        # DotsOCR parameters with defaults
        self.temperature = float(os.environ.get("DOTSOCR_TEMPERATURE", "0.1"))
        self.top_p = float(os.environ.get("DOTSOCR_TOP_P", "1.0"))
        self.max_completion_tokens = int(os.environ.get("DOTSOCR_MAX_TOKENS", "16384"))
        self.num_threads = int(os.environ.get("DOTSOCR_NUM_THREADS", "64"))
        self.dpi = int(os.environ.get("DOTSOCR_DPI", "200"))
        
        # Initialize DotsOCR parser
        self.parser = DotsOCRParser(
            ip=self.ip,
            port=self.port,
            model_name=self.model_name,
            temperature=self.temperature,
            top_p=self.top_p,
            max_completion_tokens=self.max_completion_tokens,
            num_thread=self.num_threads,
            dpi=self.dpi,
            output_dir=tempfile.gettempdir()
        )
        
        logger.info(f"DotsOCR provider initialized with server {self.ip}:{self.port}")

    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute OCR task asynchronously."""
        # DotsOCR parser is synchronous, so we run it in thread pool
        return await self._execute_with_backoff_async(task)

    def _execute_task_sync(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute OCR task synchronously."""
        file_path = task.get("file_path")
        file_content = task.get("file_content")
        file_name = task.get("file_name")
        prompt_mode = task.get("prompt_mode", "prompt_layout_all_en")
        
        if not file_path and not (file_content and file_name):
            raise ValueError("Either file_path or (file_content and file_name) must be provided")
        
        # Create temporary file if content is provided
        if file_content:
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file_name)[1]) as tmp_file:
                tmp_file.write(file_content)
                file_path = tmp_file.name
        
        try:
            # Use DotsOCR parser to process the file
            logger.info(f"Starting DotsOCR processing for file: {file_path}")
            results = self.parser.parse_file(
                input_path=file_path,
                prompt_mode=prompt_mode
            )
            logger.info(f"DotsOCR processing completed, got {len(results)} results")
            
            # Format results for AgentRag
            formatted_results = []
            for i, result in enumerate(results):
                # Extract content from DotsOCR result
                content = ""

                try:
                    # DotsOCR saves content to files, we need to read them
                    md_content_path = result.get("md_content_path")
                    if md_content_path and os.path.exists(md_content_path):
                        with open(md_content_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        logger.info(f"Read {len(content)} characters from {md_content_path}")
                    else:
                        logger.warning(f"MD content file not found: {md_content_path}")

                        # Fallback: try to read from layout info if available
                        layout_info_path = result.get("layout_info_path")
                        if layout_info_path and os.path.exists(layout_info_path):
                            with open(layout_info_path, 'r', encoding='utf-8') as f:
                                layout_data = json.load(f)

                            # Convert layout data to markdown using DotsOCR's transformer
                            from .utils.format_transformer import layoutjson2md
                            from PIL import Image

                            # Try to load the image for layout conversion
                            layout_image_path = result.get("layout_image_path")
                            if layout_image_path and os.path.exists(layout_image_path):
                                image = Image.open(layout_image_path)
                                content = layoutjson2md(image, layout_data, text_key='text')
                                logger.info(f"Generated {len(content)} characters from layout data")
                            else:
                                logger.warning(f"Layout image not found: {layout_image_path}")

                except Exception as e:
                    logger.error(f"Error reading content for page {i}: {e}")
                    content = ""

                # Clean and validate content
                if content:
                    content = content.strip()
                    # Remove excessive whitespace
                    content = '\n'.join(line.strip() for line in content.split('\n') if line.strip())

                if not content:
                    logger.warning(f"No valid content extracted for page {i}")
                else:
                    logger.info(f"Successfully extracted {len(content)} characters for page {i}")

                formatted_result = {
                    "page_no": result.get("page_no", i),
                    "text": content,
                    "content": content,
                    "md_content": content,  # Add this field for compatibility
                    "metadata": {
                        "input_height": result.get("input_height"),
                        "input_width": result.get("input_width"),
                        "filtered": result.get("filtered", False),
                        "layout_info_path": result.get("layout_info_path"),
                        "layout_image_path": result.get("layout_image_path"),
                        "md_content_path": result.get("md_content_path"),
                        "content_length": len(content)
                    }
                }
                formatted_results.append(formatted_result)
            
            # Validate final results
            valid_results = [r for r in formatted_results if r["content"].strip()]
            total_content_length = sum(len(r["content"]) for r in formatted_results)

            logger.info(f"Processing complete: {len(valid_results)}/{len(formatted_results)} pages with content")
            logger.info(f"Total content length: {total_content_length} characters")

            return {
                "success": True,
                "results": formatted_results,
                "total_pages": len(formatted_results),
                "pages_with_content": len(valid_results),
                "total_content_length": total_content_length
            }
            
        except Exception as e:
            logger.error(f"DotsOCR processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
        finally:
            # Clean up temporary file if created
            if file_content and file_path and os.path.exists(file_path):
                try:
                    os.unlink(file_path)
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary file {file_path}: {e}")

    async def upload_file(
        self,
        file_path: str | None = None,
        file_content: bytes | None = None,
        file_name: str | None = None,
    ) -> Dict[str, Any]:
        """
        Upload a file for OCR processing.
        
        Args:
            file_path: Path to the file to upload
            file_content: Binary content of the file
            file_name: Name of the file (required if file_content is provided)
            
        Returns:
            File information dictionary
        """
        if file_path:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            file_name = os.path.basename(file_path)
            with open(file_path, "rb") as f:
                file_content = f.read()
        elif not file_content or not file_name:
            raise ValueError(
                "Either file_path or (file_content and file_name) must be provided"
            )
        
        # Generate a unique file ID
        file_id = str(uuid.uuid4())
        
        return {
            "file_id": file_id,
            "file_name": file_name,
            "file_content": file_content,
            "file_size": len(file_content)
        }

    async def process_file(
        self, file_id: str, include_image_base64: bool = False
    ) -> Dict[str, Any]:
        """
        Process a previously uploaded file using its file ID.
        
        Args:
            file_id: ID of the file to process
            include_image_base64: Whether to include image base64 in the response
            
        Returns:
            OCR response
        """
        # Note: In a real implementation, you would retrieve the file info
        # from a database or cache using the file_id
        raise NotImplementedError(
            "process_file with file_id is not implemented. "
            "Use upload_file + direct processing or process_pdf instead."
        )

    async def process_url(
        self,
        url: str,
        is_image: bool = False,
        include_image_base64: bool = False,
    ) -> Dict[str, Any]:
        """
        Process a document or image from a URL.
        
        Args:
            url: URL of the document or image
            is_image: Whether the URL points to an image
            include_image_base64: Whether to include image base64 in the response
            
        Returns:
            OCR response
        """
        import requests
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            
            # Determine file name from URL
            file_name = os.path.basename(url) or "document"
            if not os.path.splitext(file_name)[1]:
                file_name += ".pdf" if not is_image else ".jpg"
            
            # Create task for processing
            task = {
                "file_content": response.content,
                "file_name": file_name,
                "prompt_mode": "prompt_layout_all_en"
            }
            
            return self._execute_task_sync(task)
            
        except Exception as e:
            logger.error(f"Error processing URL {url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }

    async def process_pdf(
        self, file_path: str | None = None, file_content: bytes | None = None
    ) -> Dict[str, Any]:
        """
        Process a PDF file.
        
        Args:
            file_path: Path to the PDF file
            file_content: Binary content of the PDF file
            
        Returns:
            OCR response
        """
        try:
            if file_path:
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"PDF file not found: {file_path}")
                file_name = os.path.basename(file_path)
                with open(file_path, "rb") as f:
                    file_content = f.read()
            elif not file_content:
                raise ValueError("Either file_path or file_content must be provided")
            else:
                file_name = f"document_{uuid.uuid4().hex[:8]}.pdf"
            
            # Create task for processing
            task = {
                "file_content": file_content,
                "file_name": file_name,
                "prompt_mode": "prompt_layout_all_en"
            }
            
            return self._execute_task_sync(task)
            
        except Exception as e:
            logger.error(f"Error processing PDF: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
