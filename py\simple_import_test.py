#!/usr/bin/env python3
"""
简单的导入测试
"""

try:
    print("Testing DotsOCR import...")
    from core.providers.ocr.dotsocr import DotsOCRProvider
    print("✅ DotsOCR import successful!")
    
    from core.base.providers.ocr import OCRConfig
    print("✅ OCRConfig import successful!")
    
    # Test initialization
    config = OCRConfig(provider="dotsocr", model="test-model")
    provider = DotsOCRProvider(config)
    print("✅ DotsOCR initialization successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
