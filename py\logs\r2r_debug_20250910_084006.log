2025-09-10 08:40:09,945 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 08:40:09,947 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 08:40:10,414 - httpcore.connection - DEBUG - trace:47 - connect_tcp.started host='raw.githubusercontent.com' port=443 local_address=None timeout=5 socket_options=None
2025-09-10 08:40:10,465 - httpcore.connection - DEBUG - trace:47 - connect_tcp.failed exception=ConnectError(gaierror(11004, 'getaddrinfo failed'))
2025-09-10 08:40:11,264 - LiteLLM - DEBUG - <module>:179 - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-10 08:40:11,990 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 08:40:11,991 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 08:40:12,212 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 08:40:12,214 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 08:40:12,443 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 08:40:12,445 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
