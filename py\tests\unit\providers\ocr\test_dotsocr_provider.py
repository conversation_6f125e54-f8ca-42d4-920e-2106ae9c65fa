"""
Unit tests for DotsOCR Provider
"""
import pytest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
from PIL import Image
import io

from core.base.providers.ocr import OCRConfig
from core.providers.ocr.dotsocr import DotsOCRProvider
from core.providers.ocr.dotsocr.exceptions import (
    DotsOCRConfigurationError, DotsOCRValidationError, 
    DotsOCRProcessingError, DotsOCRConnectionError
)


class TestDotsOCRProvider:
    """Test suite for DotsOCRProvider"""
    
    @pytest.fixture
    def valid_config(self):
        """Create a valid OCR config for testing"""
        return OCRConfig(provider="dotsocr", model="test-model")
    
    @pytest.fixture
    def provider(self, valid_config):
        """Create a DotsOCRProvider instance for testing"""
        with patch.dict(os.environ, {
            'DOTSOCR_IP': 'localhost',
            'DOTSOCR_PORT': '8000',
            'DOTSOCR_MODEL': 'test-model'
        }):
            with patch('core.providers.ocr.dotsocr.provider.DotsOCRParser'):
                return DotsOCRProvider(valid_config)
    
    @pytest.fixture
    def test_image(self):
        """Create a test image for testing"""
        image = Image.new('RGB', (100, 100), color='white')
        return image
    
    @pytest.fixture
    def test_image_bytes(self, test_image):
        """Create test image as bytes"""
        img_bytes = io.BytesIO()
        test_image.save(img_bytes, format='PNG')
        return img_bytes.getvalue()
    
    def test_init_with_valid_config(self, valid_config):
        """Test initialization with valid configuration"""
        with patch.dict(os.environ, {
            'DOTSOCR_IP': 'localhost',
            'DOTSOCR_PORT': '8000'
        }):
            with patch('core.providers.ocr.dotsocr.provider.DotsOCRParser'):
                provider = DotsOCRProvider(valid_config)
                assert provider.config == valid_config
                assert provider.ip == 'localhost'
                assert provider.port == 8000
    
    def test_init_with_invalid_config(self):
        """Test initialization with invalid configuration"""
        with pytest.raises(DotsOCRConfigurationError):
            DotsOCRProvider("invalid_config")
    
    def test_init_with_invalid_port(self, valid_config):
        """Test initialization with invalid port"""
        with patch.dict(os.environ, {'DOTSOCR_PORT': '99999'}):
            with pytest.raises(DotsOCRConfigurationError):
                DotsOCRProvider(valid_config)
    
    def test_init_with_invalid_temperature(self, valid_config):
        """Test initialization with invalid temperature"""
        with patch.dict(os.environ, {'DOTSOCR_TEMPERATURE': '5.0'}):
            with pytest.raises(DotsOCRConfigurationError):
                DotsOCRProvider(valid_config)
    
    def test_validate_config(self, provider):
        """Test configuration validation"""
        # Test valid configuration
        provider._validate_config()  # Should not raise
        
        # Test invalid port
        provider.port = 99999
        with pytest.raises(DotsOCRConfigurationError):
            provider._validate_config()
    
    @pytest.mark.asyncio
    async def test_upload_file_with_path(self, provider):
        """Test file upload with file path"""
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            # Create a test image file
            test_image = Image.new('RGB', (100, 100), color='white')
            test_image.save(tmp_file.name, 'PNG')
            
            try:
                result = await provider.upload_file(file_path=tmp_file.name)
                
                assert 'file_id' in result
                assert 'file_name' in result
                assert 'file_path' in result
                assert 'file_size' in result
                assert result['file_size'] > 0
                
            finally:
                os.unlink(tmp_file.name)
    
    @pytest.mark.asyncio
    async def test_upload_file_with_content(self, provider, test_image_bytes):
        """Test file upload with file content"""
        result = await provider.upload_file(
            file_content=test_image_bytes,
            file_name='test.png'
        )
        
        assert 'file_id' in result
        assert result['file_name'] == 'test.png'
        assert result['file_size'] == len(test_image_bytes)
    
    @pytest.mark.asyncio
    async def test_upload_file_invalid_input(self, provider):
        """Test file upload with invalid input"""
        with pytest.raises(ValueError):
            await provider.upload_file()  # No file_path or file_content
    
    @pytest.mark.asyncio
    async def test_upload_file_nonexistent_path(self, provider):
        """Test file upload with non-existent file path"""
        with pytest.raises(FileNotFoundError):
            await provider.upload_file(file_path='/nonexistent/file.png')
    
    @pytest.mark.asyncio
    async def test_process_url_success(self, provider):
        """Test successful URL processing"""
        mock_response = Mock()
        mock_response.content = b'fake_image_data'
        mock_response.headers = {'content-type': 'image/png'}
        mock_response.raise_for_status = Mock()
        
        with patch('requests.get', return_value=mock_response):
            with patch.object(provider, '_execute_task', new_callable=AsyncMock) as mock_execute:
                mock_execute.return_value = {
                    'success': True,
                    'results': [],
                    'total_pages': 1
                }
                
                result = await provider.process_url('https://example.com/image.png')
                
                assert result['success'] is True
                mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_pdf_success(self, provider):
        """Test successful PDF processing"""
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            tmp_file.write(b'fake_pdf_content')
            
            try:
                with patch.object(provider, '_execute_task', new_callable=AsyncMock) as mock_execute:
                    mock_execute.return_value = {
                        'success': True,
                        'results': [{'page_no': 0}],
                        'total_pages': 1
                    }
                    
                    result = await provider.process_pdf(file_path=tmp_file.name)
                    
                    assert result['success'] is True
                    assert result['total_pages'] == 1
                    mock_execute.assert_called_once()
                    
            finally:
                os.unlink(tmp_file.name)
    
    @pytest.mark.asyncio
    async def test_process_pdf_nonexistent_file(self, provider):
        """Test PDF processing with non-existent file"""
        with pytest.raises(FileNotFoundError):
            await provider.process_pdf(file_path='/nonexistent/file.pdf')
    
    def test_get_supported_formats(self, provider):
        """Test getting supported file formats"""
        formats = provider.get_supported_formats()
        assert '.png' in formats
        assert '.jpg' in formats
        assert '.jpeg' in formats
        assert '.pdf' in formats
    
    def test_get_model_info(self, provider):
        """Test getting model information"""
        info = provider.get_model_info()
        assert info['provider'] == 'dotsocr'
        assert 'model_name' in info
        assert 'server' in info
        assert 'supported_formats' in info
        assert 'features' in info
    
    @pytest.mark.asyncio
    async def test_health_check_healthy(self, provider):
        """Test health check when service is healthy"""
        with patch.object(provider.parser.inference_client, 'inference_with_vllm') as mock_inference:
            mock_inference.return_value = "test response"
            
            result = await provider.health_check()
            
            assert result['status'] == 'healthy'
            assert 'response_time' in result
            assert 'server' in result
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy(self, provider):
        """Test health check when service is unhealthy"""
        with patch.object(provider.parser.inference_client, 'inference_with_vllm') as mock_inference:
            mock_inference.side_effect = ConnectionError("Connection failed")
            
            result = await provider.health_check()
            
            assert result['status'] == 'unhealthy'
            assert 'error' in result
    
    def test_get_performance_metrics(self, provider):
        """Test getting performance metrics"""
        metrics = provider.get_performance_metrics()
        
        assert 'provider_info' in metrics
        assert 'circuit_breaker' in metrics
        assert metrics['provider_info']['name'] == 'dotsocr'
    
    def test_reset_metrics(self, provider):
        """Test resetting metrics"""
        # This should not raise any exceptions
        provider.reset_metrics()
    
    @pytest.mark.asyncio
    async def test_execute_task_validation_error(self, provider):
        """Test _execute_task with validation error"""
        task = {}  # Empty task should cause validation error
        
        with pytest.raises(DotsOCRValidationError):
            await provider._execute_task(task)
    
    @pytest.mark.asyncio
    async def test_execute_task_with_metrics(self, provider, test_image_bytes):
        """Test _execute_task with metrics collection"""
        task = {
            'file_content': test_image_bytes,
            'file_name': 'test.png',
            'prompt_mode': 'prompt_layout_all_en'
        }
        
        with patch.object(provider.parser, 'parse_file') as mock_parse:
            mock_parse.return_value = [{'page_no': 0}]
            
            result = await provider._execute_task(task)
            
            assert result['success'] is True
            assert 'operation_id' in result
            assert 'processing_time' in result
