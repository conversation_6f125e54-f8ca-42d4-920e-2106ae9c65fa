2025-09-10 09:40:08,488 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 09:40:08,490 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 09:40:09,376 - httpcore.connection - DEBUG - trace:47 - connect_tcp.started host='127.0.0.1' port=10809 local_address=None timeout=5 socket_options=None
2025-09-10 09:40:09,379 - httpcore.connection - DEBUG - trace:47 - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000220022CA600>
2025-09-10 09:40:09,380 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.started request=<Request [b'CONNECT']>
2025-09-10 09:40:09,389 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.complete
2025-09-10 09:40:09,389 - httpcore.http11 - DEBUG - trace:47 - send_request_body.started request=<Request [b'CONNECT']>
2025-09-10 09:40:09,389 - httpcore.http11 - DEBUG - trace:47 - send_request_body.complete
2025-09-10 09:40:09,389 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.started request=<Request [b'CONNECT']>
2025-09-10 09:40:09,389 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-09-10 09:40:09,389 - httpcore.proxy - DEBUG - trace:47 - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000220022E4F50> server_hostname='raw.githubusercontent.com' timeout=5
2025-09-10 09:40:10,385 - httpcore.proxy - DEBUG - trace:47 - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000022002001940>
2025-09-10 09:40:10,385 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.started request=<Request [b'GET']>
2025-09-10 09:40:10,385 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.complete
2025-09-10 09:40:10,385 - httpcore.http11 - DEBUG - trace:47 - send_request_body.started request=<Request [b'GET']>
2025-09-10 09:40:10,385 - httpcore.http11 - DEBUG - trace:47 - send_request_body.complete
2025-09-10 09:40:10,385 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.started request=<Request [b'GET']>
2025-09-10 09:40:10,739 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Connection', b'keep-alive'), (b'Content-Length', b'36445'), (b'Cache-Control', b'max-age=300'), (b'Content-Security-Policy', b"default-src 'none'; style-src 'unsafe-inline'; sandbox"), (b'Content-Type', b'text/plain; charset=utf-8'), (b'ETag', b'W/"0162f5c2047bcdd4238d10cf0237d8d3fd664646643b2030f39196c30a720555"'), (b'Strict-Transport-Security', b'max-age=31536000'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Frame-Options', b'deny'), (b'X-XSS-Protection', b'1; mode=block'), (b'X-GitHub-Request-Id', b'E2CA:30B92B:113DE9:1621AF:68C0934C'), (b'Content-Encoding', b'gzip'), (b'Accept-Ranges', b'bytes'), (b'Date', b'Wed, 10 Sep 2025 01:40:11 GMT'), (b'Via', b'1.1 varnish'), (b'X-Served-By', b'cache-bfi-krnt7300035-BFI'), (b'X-Cache', b'HIT'), (b'X-Cache-Hits', b'21'), (b'X-Timer', b'S1757468411.122003,VS0,VE0'), (b'Vary', b'Authorization,Accept-Encoding'), (b'Access-Control-Allow-Origin', b'*'), (b'Cross-Origin-Resource-Policy', b'cross-origin'), (b'X-Fastly-Request-ID', b'a5f5f7f89781038698d3b3276243b8a325e8ecfe'), (b'Expires', b'Wed, 10 Sep 2025 01:45:11 GMT'), (b'Source-Age', b'216')])
2025-09-10 09:40:10,742 - httpcore.http11 - DEBUG - trace:47 - receive_response_body.started request=<Request [b'GET']>
2025-09-10 09:40:16,153 - httpcore.http11 - DEBUG - trace:47 - receive_response_body.failed exception=ReadTimeout(TimeoutError('The read operation timed out'))
2025-09-10 09:40:16,153 - httpcore.http11 - DEBUG - trace:47 - response_closed.started
2025-09-10 09:40:16,154 - httpcore.http11 - DEBUG - trace:47 - response_closed.complete
2025-09-10 09:40:16,936 - LiteLLM - DEBUG - <module>:179 - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-10 09:40:17,598 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 09:40:17,599 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 09:40:17,834 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 09:40:17,834 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 09:40:18,072 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 09:40:18,074 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
