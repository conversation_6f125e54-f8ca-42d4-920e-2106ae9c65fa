2025-09-09 20:01:29,532 - <PERSON>teLLM - DEBUG - Using AiohttpTransport...
2025-09-09 20:01:29,536 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 20:01:31,009 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=10809 local_address=None timeout=5 socket_options=None
2025-09-09 20:01:31,011 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001DF19D16480>
2025-09-09 20:01:31,011 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-09-09 20:01:31,012 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-09-09 20:01:31,013 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-09-09 20:01:31,013 - httpcore.http11 - DEBUG - send_request_body.complete
2025-09-09 20:01:31,014 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-09-09 20:01:31,014 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-09-09 20:01:31,015 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001DF19CF5E50> server_hostname='raw.githubusercontent.com' timeout=5
2025-09-09 20:01:31,474 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001DF19D16690>
2025-09-09 20:01:31,475 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-09-09 20:01:31,477 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-09-09 20:01:31,478 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-09-09 20:01:31,479 - httpcore.http11 - DEBUG - send_request_body.complete
2025-09-09 20:01:31,480 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-09-09 20:01:31,680 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Connection', b'keep-alive'), (b'Content-Length', b'36440'), (b'Cache-Control', b'max-age=300'), (b'Content-Security-Policy', b"default-src 'none'; style-src 'unsafe-inline'; sandbox"), (b'Content-Type', b'text/plain; charset=utf-8'), (b'ETag', b'W/"d550af76ef739cf41998b2b41c0694ae0448df385dad0c2543e07bcde2a8bda4"'), (b'Strict-Transport-Security', b'max-age=31536000'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Frame-Options', b'deny'), (b'X-XSS-Protection', b'1; mode=block'), (b'X-GitHub-Request-Id', b'A2ED:39D551:15CE0A0:1CE915A:68BF7FBD'), (b'Content-Encoding', b'gzip'), (b'Accept-Ranges', b'bytes'), (b'Date', b'Tue, 09 Sep 2025 12:01:31 GMT'), (b'Via', b'1.1 varnish'), (b'X-Served-By', b'cache-bfi-krnt7300066-BFI'), (b'X-Cache', b'HIT'), (b'X-Cache-Hits', b'2'), (b'X-Timer', b'S1757419292.682279,VS0,VE0'), (b'Vary', b'Authorization,Accept-Encoding'), (b'Access-Control-Allow-Origin', b'*'), (b'Cross-Origin-Resource-Policy', b'cross-origin'), (b'X-Fastly-Request-ID', b'7278b026d3768b430b1fd0ae1db6772fd2b635ee'), (b'Expires', b'Tue, 09 Sep 2025 12:06:31 GMT'), (b'Source-Age', b'24')])
2025-09-09 20:01:31,688 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-09-09 20:01:31,963 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-09-09 20:01:31,965 - httpcore.http11 - DEBUG - response_closed.started
2025-09-09 20:01:31,966 - httpcore.http11 - DEBUG - response_closed.complete
2025-09-09 20:01:33,214 - LiteLLM - DEBUG - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-09 20:01:34,232 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 20:01:34,238 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 20:01:34,605 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 20:01:34,606 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 20:01:34,950 - LiteLLM - DEBUG - Using AiohttpTransport...
2025-09-09 20:01:34,951 - LiteLLM - DEBUG - Creating AiohttpTransport...
2025-09-09 20:01:40,951 - r2r.serve - ERROR - Failed to start server: core dependencies not installed: No module named 'core.providers.ocr.dotsocr.utils.prompts'
2025-09-09 20:01:40,954 - r2r.serve - ERROR - To run the server, install the required dependencies:
2025-09-09 20:01:40,954 - r2r.serve - ERROR - pip install 'r2r[core]'
2025-09-09 20:01:40,956 - asyncio - DEBUG - Using proactor: IocpProactor
