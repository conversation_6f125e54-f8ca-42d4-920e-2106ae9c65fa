2025-09-10 10:02:02,919 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 10:02:02,921 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 10:02:03,824 - httpcore.connection - DEBUG - trace:47 - connect_tcp.started host='127.0.0.1' port=10809 local_address=None timeout=5 socket_options=None
2025-09-10 10:02:03,824 - httpcore.connection - DEBUG - trace:47 - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001F97229D640>
2025-09-10 10:02:03,825 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.started request=<Request [b'CONNECT']>
2025-09-10 10:02:03,834 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.complete
2025-09-10 10:02:03,835 - httpcore.http11 - DEBUG - trace:47 - send_request_body.started request=<Request [b'CONNECT']>
2025-09-10 10:02:03,835 - httpcore.http11 - DEBUG - trace:47 - send_request_body.complete
2025-09-10 10:02:03,835 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.started request=<Request [b'CONNECT']>
2025-09-10 10:02:03,836 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-09-10 10:02:03,836 - httpcore.proxy - DEBUG - trace:47 - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001F972294F50> server_hostname='raw.githubusercontent.com' timeout=5
2025-09-10 10:02:04,416 - httpcore.proxy - DEBUG - trace:47 - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001F97229D820>
2025-09-10 10:02:04,416 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.started request=<Request [b'GET']>
2025-09-10 10:02:04,417 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.complete
2025-09-10 10:02:04,418 - httpcore.http11 - DEBUG - trace:47 - send_request_body.started request=<Request [b'GET']>
2025-09-10 10:02:04,418 - httpcore.http11 - DEBUG - trace:47 - send_request_body.complete
2025-09-10 10:02:04,418 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.started request=<Request [b'GET']>
2025-09-10 10:02:04,689 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Connection', b'keep-alive'), (b'Content-Length', b'36445'), (b'Cache-Control', b'max-age=300'), (b'Content-Security-Policy', b"default-src 'none'; style-src 'unsafe-inline'; sandbox"), (b'Content-Type', b'text/plain; charset=utf-8'), (b'ETag', b'W/"0162f5c2047bcdd4238d10cf0237d8d3fd664646643b2030f39196c30a720555"'), (b'Strict-Transport-Security', b'max-age=31536000'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Frame-Options', b'deny'), (b'X-XSS-Protection', b'1; mode=block'), (b'X-GitHub-Request-Id', b'E2CA:30B92B:113DE9:1621AF:68C0934C'), (b'Content-Encoding', b'gzip'), (b'Accept-Ranges', b'bytes'), (b'Date', b'Wed, 10 Sep 2025 02:02:05 GMT'), (b'Via', b'1.1 varnish'), (b'X-Served-By', b'cache-bfi-krnt7300064-BFI'), (b'X-Cache', b'HIT'), (b'X-Cache-Hits', b'4'), (b'X-Timer', b'S1757469725.165320,VS0,VE0'), (b'Vary', b'Authorization,Accept-Encoding'), (b'Access-Control-Allow-Origin', b'*'), (b'Cross-Origin-Resource-Policy', b'cross-origin'), (b'X-Fastly-Request-ID', b'19524409f07409cf2b2c4318216aa08e78c575a1'), (b'Expires', b'Wed, 10 Sep 2025 02:07:05 GMT'), (b'Source-Age', b'29')])
2025-09-10 10:02:04,690 - httpcore.http11 - DEBUG - trace:47 - receive_response_body.started request=<Request [b'GET']>
2025-09-10 10:02:05,047 - httpcore.http11 - DEBUG - trace:47 - receive_response_body.complete
2025-09-10 10:02:05,047 - httpcore.http11 - DEBUG - trace:47 - response_closed.started
2025-09-10 10:02:05,048 - httpcore.http11 - DEBUG - trace:47 - response_closed.complete
2025-09-10 10:02:05,802 - LiteLLM - DEBUG - <module>:179 - [Non-Blocking] Unable to import GenericAPILogger - LiteLLM Enterprise Feature - No module named 'litellm_enterprise'
2025-09-10 10:02:06,441 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 10:02:06,442 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 10:02:06,670 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 10:02:06,671 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
2025-09-10 10:02:06,893 - LiteLLM - DEBUG - _should_use_aiohttp_transport:579 - Using AiohttpTransport...
2025-09-10 10:02:06,894 - LiteLLM - DEBUG - _create_aiohttp_transport:636 - Creating AiohttpTransport...
