"""
Test DotsOCR configuration integration with r2r.toml
"""
import pytest
import tempfile
import os
from unittest.mock import patch

from core.main.config import R2RConfig
from core.main.assembly.factory import R2RProviderFactory
from core.base.providers.ocr import OCRConfig
from core.providers.ocr.dotsocr import DotsOCRProvider


class TestDotsOCRConfiguration:
    """Test DotsOCR configuration integration"""
    
    def test_ocr_config_supports_dotsocr(self):
        """Test that OCRConfig supports dotsocr provider"""
        config = OCRConfig(provider="dotsocr", model="dotsocr-model")
        
        # Should not raise an exception
        config.validate_config()
        
        # Check that dotsocr is in supported providers
        assert "dotsocr" in config.supported_providers
    
    def test_factory_creates_dotsocr_provider(self):
        """Test that factory can create DotsOCR provider"""
        config = OCRConfig(provider="dotsocr", model="dotsocr-model")
        
        with patch.dict(os.environ, {
            'DOTSOCR_IP': 'localhost',
            'DOTSOCR_PORT': '8000'
        }):
            with patch('core.providers.ocr.dotsocr.provider.DotsOCRParser'):
                provider = R2RProviderFactory.create_ocr_provider(config)
                
                assert isinstance(provider, DotsOCRProvider)
                assert provider.config.provider == "dotsocr"
                assert provider.config.model == "dotsocr-model"
    
    def test_r2r_config_with_dotsocr(self):
        """Test R2R configuration with DotsOCR settings"""
        # Create a test configuration file
        test_config = """
[app]
default_max_documents_per_user = 1000

[auth]
provider = "r2r"
require_authentication = false

[completion]
provider = "r2r"

[crypto]
provider = "bcrypt"

[file]
provider = "postgres"

[database]
default_collection_name = "Test"

[embedding]
provider = "ollama"
base_model = "test-model"
base_dimension = 512
batch_size = 32

[completion_embedding]
provider = "ollama"
base_model = "test-model"
base_dimension = 512
batch_size = 32

[ingestion]
provider = "r2r"
chunking_strategy = "recursive"
chunk_size = 512
chunk_overlap = 256

[ocr]
provider = "dotsocr"
model = "dotsocr-model"

[orchestration]
provider = "simple"

[email]
provider = "console_mock"

[scheduler]
provider = "apscheduler"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write(test_config)
            config_path = f.name
        
        try:
            # Load configuration
            config = R2RConfig.load(config_path=config_path)
            
            # Check OCR configuration
            assert config.ocr.provider == "dotsocr"
            assert config.ocr.model == "dotsocr-model"
            
            # Validate OCR config
            config.ocr.validate_config()
            
        finally:
            os.unlink(config_path)
    
    def test_environment_variable_integration(self):
        """Test that environment variables are properly integrated"""
        test_env = {
            'DOTSOCR_IP': '*************',
            'DOTSOCR_PORT': '9000',
            'DOTSOCR_MODEL': 'custom-dotsocr-model',
            'DOTSOCR_DPI': '300',
            'DOTSOCR_TEMPERATURE': '0.2',
            'DOTSOCR_TOP_P': '0.9',
            'DOTSOCR_MAX_TOKENS': '8192',
            'DOTSOCR_NUM_THREADS': '16'
        }
        
        config = OCRConfig(provider="dotsocr", model="test-model")
        
        with patch.dict(os.environ, test_env):
            with patch('core.providers.ocr.dotsocr.provider.DotsOCRParser'):
                provider = DotsOCRProvider(config)
                
                # Check that environment variables are used
                assert provider.ip == '*************'
                assert provider.port == 9000
                assert provider.model_name == 'custom-dotsocr-model'  # From env, not config
                assert provider.dpi == 300
                assert provider.temperature == 0.2
                assert provider.top_p == 0.9
                assert provider.max_completion_tokens == 8192
                assert provider.num_threads == 16
    
    def test_config_validation_errors(self):
        """Test configuration validation with invalid values"""
        from core.providers.ocr.dotsocr.exceptions import DotsOCRConfigurationError
        
        # Test invalid provider
        with pytest.raises(ValueError):
            config = OCRConfig(provider="invalid_provider")
            config.validate_config()
        
        # Test invalid environment values
        invalid_env_configs = [
            {'DOTSOCR_PORT': '99999'},  # Invalid port
            {'DOTSOCR_DPI': '1000'},    # Invalid DPI
            {'DOTSOCR_TEMPERATURE': '5.0'},  # Invalid temperature
            {'DOTSOCR_TOP_P': '2.0'},   # Invalid top_p
            {'DOTSOCR_NUM_THREADS': '100'},  # Invalid thread count
        ]
        
        for invalid_env in invalid_env_configs:
            config = OCRConfig(provider="dotsocr", model="test-model")
            
            with patch.dict(os.environ, invalid_env):
                with pytest.raises(DotsOCRConfigurationError):
                    DotsOCRProvider(config)
    
    def test_default_configuration_values(self):
        """Test that default configuration values are properly set"""
        config = OCRConfig(provider="dotsocr", model="test-model")
        
        with patch('core.providers.ocr.dotsocr.provider.DotsOCRParser'):
            provider = DotsOCRProvider(config)
            
            # Check default values
            assert provider.ip == "localhost"
            assert provider.port == 8000
            assert provider.dpi == 200
            assert provider.temperature == 0.1
            assert provider.top_p == 1.0
            assert provider.max_completion_tokens == 16384
            assert provider.num_threads == 8
    
    def test_config_override_priority(self):
        """Test configuration override priority (env > config > default)"""
        # Config specifies one model, env specifies another
        config = OCRConfig(provider="dotsocr", model="config-model")
        
        with patch.dict(os.environ, {'DOTSOCR_MODEL': 'env-model'}):
            with patch('core.providers.ocr.dotsocr.provider.DotsOCRParser'):
                provider = DotsOCRProvider(config)
                
                # Environment should override config
                assert provider.model_name == 'env-model'
    
    def test_missing_required_config(self):
        """Test behavior with missing required configuration"""
        # Test with no provider specified
        with pytest.raises(ValueError):
            config = OCRConfig()
            config.validate_config()
    
    def test_factory_error_handling(self):
        """Test factory error handling for unsupported providers"""
        config = OCRConfig(provider="unsupported_provider")
        
        with pytest.raises(ValueError, match="not supported"):
            R2RProviderFactory.create_ocr_provider(config)
    
    def test_integration_with_ingestion_config(self):
        """Test integration with ingestion configuration"""
        # Create a test configuration that includes ingestion settings
        test_config = """
[ingestion]
provider = "r2r"
chunking_strategy = "recursive"
chunk_size = 1024
chunk_overlap = 512
extra_parsers = { pdf = ["zerox", "ocr"] }

[ocr]
provider = "dotsocr"
model = "dotsocr-model"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write(test_config)
            config_path = f.name
        
        try:
            # This should load without errors
            config = R2RConfig.load(config_path=config_path)
            
            # Check that OCR is properly configured
            assert config.ocr.provider == "dotsocr"
            
            # Check that ingestion can use OCR
            assert "ocr" in config.ingestion.extra_parsers.get("pdf", [])
            
        finally:
            os.unlink(config_path)


def test_sample_r2r_toml_with_dotsocr():
    """Test a complete r2r.toml configuration with DotsOCR"""
    sample_config = """
[app]
default_max_documents_per_user = 10_000
fast_llm = "openai/gpt-4o-mini"
quality_llm = "openai/gpt-4o"

[auth]
provider = "r2r"
require_authentication = false

[completion]
provider = "r2r"

[crypto]
provider = "bcrypt"

[file]
provider = "postgres"

[database]
default_collection_name = "Default"

[embedding]
provider = "openai"
base_model = "text-embedding-3-small"
base_dimension = 512
batch_size = 128

[completion_embedding]
provider = "openai"
base_model = "text-embedding-3-small"
base_dimension = 512
batch_size = 128

[ingestion]
provider = "r2r"
chunking_strategy = "recursive"
chunk_size = 1024
chunk_overlap = 512

  [ingestion.extra_parsers]
  pdf = ["zerox", "ocr"]

[ocr]
provider = "dotsocr"
model = "dotsocr-model"

[orchestration]
provider = "simple"

[email]
provider = "console_mock"

[scheduler]
provider = "apscheduler"
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        f.write(sample_config)
        config_path = f.name
    
    try:
        # This should load and validate successfully
        config = R2RConfig.load(config_path=config_path)
        
        # Verify DotsOCR configuration
        assert config.ocr.provider == "dotsocr"
        assert config.ocr.model == "dotsocr-model"
        
        # Verify OCR is enabled in ingestion
        assert "ocr" in config.ingestion.extra_parsers["pdf"]
        
        # Validate the configuration
        config.ocr.validate_config()
        
    finally:
        os.unlink(config_path)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
