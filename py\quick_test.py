#!/usr/bin/env python3
"""
快速测试导入和启动
"""
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_basic_import():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        # 测试最基本的导入
        from core.providers.ocr.dotsocr.utils.prompts import dict_promptmode_to_prompt
        print("✅ prompts 导入成功")
        
        from core.providers.ocr.dotsocr.utils.consts import image_extensions
        print("✅ consts 导入成功")
        
        from core.providers.ocr.dotsocr.parser import DotsOCRParser
        print("✅ parser 导入成功")
        
        from core.providers.ocr.dotsocr.provider import DotsOCRProvider
        print("✅ provider 导入成功")
        
        print("🎉 所有基本导入成功！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_r2r_start():
    """测试R2R启动"""
    print("\n🚀 测试R2R启动...")
    
    try:
        import os
        
        # 设置环境变量
        os.environ["R2R_LOG_LEVEL"] = "DEBUG"
        os.environ["DOTSOCR_DEBUG"] = "true"
        
        # 加载环境变量文件
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if key not in os.environ:
                            os.environ[key] = value
        
        # 设置配置文件路径
        config_path = Path(__file__).parent / 'r2r' / 'r2r.toml'
        os.environ['R2R_CONFIG_PATH'] = str(config_path)
        
        print(f"📁 配置文件: {config_path}")
        print(f"🌐 服务地址: {os.getenv('R2R_HOST', '0.0.0.0')}:{os.getenv('R2R_PORT', '7272')}")
        
        # 尝试导入R2R
        from r2r.serve import main as serve_main
        print("✅ R2R导入成功")
        
        print("🚀 启动R2R服务...")
        serve_main()
        
    except Exception as e:
        print(f"❌ R2R启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 快速测试和启动")
    print("=" * 40)
    
    # 测试导入
    if not test_basic_import():
        print("❌ 导入测试失败，无法启动")
        return False
    
    # 测试启动
    test_r2r_start()
    
    return True

if __name__ == "__main__":
    main()
